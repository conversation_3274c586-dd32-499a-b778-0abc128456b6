{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "baseUrl": ".", "paths": {"configs/*": ["../../../configs/*"], "lib/*": ["../../../lib/*"], "toolkit/*": ["../../../toolkit/*"], "types/*": ["../../../types/*"]}, "types": ["node"], "allowImportingTsExtensions": true, "noEmit": true}, "include": ["index.ts", "../app/**/*"], "exclude": ["node_modules", "dist"]}