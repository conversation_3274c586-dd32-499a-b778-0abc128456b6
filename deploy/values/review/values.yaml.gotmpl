fullNameOverride: bs-stack
nameOverride: bs-stack
imagePullSecrets:
  - name: regcred
config:
  network:
    id: "********"
    name: Blockscout
    shortname: Blockscout
    currency:
      name: Ether
      symbol: ETH
      decimals: 18
  account:
    enabled: true
  testnet: true
blockscout:
  enabled: false
stats:
  enabled: false
frontend:
  enabled: true
  replicaCount: 1
  image:
    repository: ghcr.io/blockscout/frontend-private
    tag: review-{{ requiredEnv "GITHUB_REF_NAME_SLUG" }}
    pullPolicy: Always
  ingress:
    enabled: true
    annotations:
      kubernetes.io/ingress.class: internal-and-public
      nginx.ingress.kubernetes.io/proxy-body-size: 500m
      nginx.ingress.kubernetes.io/client-max-body-size: "500M"
      nginx.ingress.kubernetes.io/proxy-buffering: "on"
      nginx.ingress.kubernetes.io/proxy-connect-timeout: "15m"
      nginx.ingress.kubernetes.io/proxy-send-timeout: "15m"
      nginx.ingress.kubernetes.io/proxy-read-timeout: "15m"
      nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"
      nginx.ingress.kubernetes.io/proxy-buffers-number: "8"
      cert-manager.io/cluster-issuer: "zerossl-prod"
    hostname: review-{{ requiredEnv "GITHUB_REF_NAME_SLUG" }}.k8s-dev.blockscout.com

  resources:
    limits:
      memory: 768Mi
      cpu: "1"
    requests:
      memory: 384Mi
      cpu: 250m
  env:
    NEXT_PUBLIC_APP_ENV: review
    NEXT_PUBLIC_FEATURED_NETWORKS: https://raw.githubusercontent.com/blockscout/frontend-configs/dev/configs/featured-networks/eth-sepolia.json
    NEXT_PUBLIC_FEATURED_NETWORKS_ALL_LINK: https://chains.blockscout.com/
    NEXT_PUBLIC_NETWORK_LOGO: https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/network-logos/sepolia.svg
    NEXT_PUBLIC_NETWORK_ICON: https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/network-icons/sepolia.png
    NEXT_PUBLIC_API_HOST: eth-sepolia.k8s-dev.blockscout.com
    NEXT_PUBLIC_STATS_API_HOST: https://stats-sepolia.k8s-dev.blockscout.com/
    NEXT_PUBLIC_VISUALIZE_API_HOST: http://visualizer-svc.visualizer-testing.svc.cluster.local/
    NEXT_PUBLIC_CONTRACT_INFO_API_HOST: https://contracts-info-test.k8s-dev.blockscout.com
    NEXT_PUBLIC_ADMIN_SERVICE_API_HOST: https://admin-rs-test.k8s-dev.blockscout.com
    NEXT_PUBLIC_NAME_SERVICE_API_HOST: https://bens-rs-test.k8s-dev.blockscout.com
    NEXT_PUBLIC_METADATA_SERVICE_API_HOST: https://metadata-test.k8s-dev.blockscout.com
    NEXT_PUBLIC_HOMEPAGE_CHARTS: "['daily_txs','coin_price','market_cap']"
    NEXT_PUBLIC_NETWORK_RPC_URL: https://eth-sepolia.public.blastapi.io
    NEXT_PUBLIC_NETWORK_EXPLORERS: "[{'title':'Bitquery','baseUrl':'https://explorer.bitquery.io/','paths':{'tx':'/goerli/tx','address':'/goerli/address','token':'/goerli/token','block':'/goerli/block'}},{'title':'Etherscan','logo':'https://github.com/blockscout/frontend-configs/blob/main/configs/explorer-logos/etherscan.png?raw=true','baseUrl':'https://goerli.etherscan.io/','paths':{'tx':'/tx','address':'/address','token':'/token','block':'/block'}}]"
    NEXT_PUBLIC_GRAPHIQL_TRANSACTION: 0xf7d4972356e6ae44ae948d0cf19ef2beaf0e574c180997e969a2837da15e349d
    NEXT_PUBLIC_WEB3_WALLETS: "['token_pocket','coinbase','metamask']"
    NEXT_PUBLIC_VIEWS_ADDRESS_IDENTICON_TYPE: gradient_avatar
    NEXT_PUBLIC_VIEWS_CONTRACT_SOLIDITYSCAN_ENABLED: true
    NEXT_PUBLIC_USE_NEXT_JS_PROXY: true
    NEXT_PUBLIC_VIEWS_NFT_MARKETPLACES: "[{'name':'LooksRare','collection_url':'https://goerli.looksrare.org/collections/{hash}','instance_url':'https://goerli.looksrare.org/collections/{hash}/{id}','logo_url':'https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/nft-marketplace-logos/looks-rare.png'}]"
    NEXT_PUBLIC_HAS_USER_OPS: true
    NEXT_PUBLIC_CONTRACT_CODE_IDES: "[{'title':'Remix IDE','url':'https://remix.blockscout.com/?address={hash}&blockscout=eth-goerli.blockscout.com','icon_url':'https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/ide-icons/remix.png'}]"
    NEXT_PUBLIC_TRANSACTION_INTERPRETATION_PROVIDER: blockscout
    NEXT_PUBLIC_HAS_CONTRACT_AUDIT_REPORTS: true
    NEXT_PUBLIC_AD_BANNER_PROVIDER: slise
    NEXT_PUBLIC_DATA_AVAILABILITY_ENABLED: true
    NEXT_PUBLIC_NAVIGATION_HIGHLIGHTED_ROUTES: "['/apps']"
    PROMETHEUS_METRICS_ENABLED: true
    NEXT_PUBLIC_OG_ENHANCED_DATA_ENABLED: true
    NEXT_PUBLIC_VIEWS_TOKEN_SCAM_TOGGLE_ENABLED: true
    NEXT_PUBLIC_CLUSTERS_API_HOST: https://api.clusters.xyz
    NEXT_PUBLIC_CLUSTERS_CDN_URL: https://cdn.clusters.xyz
  envFromSecret:
    NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID: ref+vault://deployment-values/blockscout/dev/review?token_env=VAULT_TOKEN&address=https://vault.k8s.blockscout.com#/NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID
    NEXT_PUBLIC_GOOGLE_ANALYTICS_PROPERTY_ID: ref+vault://deployment-values/blockscout/dev/review?token_env=VAULT_TOKEN&address=https://vault.k8s.blockscout.com#/NEXT_PUBLIC_GOOGLE_ANALYTICS_PROPERTY_ID
    NEXT_PUBLIC_GROWTH_BOOK_CLIENT_KEY: ref+vault://deployment-values/blockscout/dev/review?token_env=VAULT_TOKEN&address=https://vault.k8s.blockscout.com#/NEXT_PUBLIC_GROWTH_BOOK_CLIENT_KEY
    NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN: ref+vault://deployment-values/blockscout/common?token_env=VAULT_TOKEN&address=https://vault.k8s.blockscout.com#/NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN
    NEXT_PUBLIC_RE_CAPTCHA_APP_SITE_KEY: ref+vault://deployment-values/blockscout/eth-sepolia/testnet?token_env=VAULT_TOKEN&address=https://vault.k8s.blockscout.com#/RE_CAPTCHA_CLIENT_KEY
    NEXT_PUBLIC_ROLLBAR_CLIENT_TOKEN: ref+vault://deployment-values/blockscout/common?token_env=VAULT_TOKEN&address=https://vault.k8s.blockscout.com#/NEXT_PUBLIC_ROLLBAR_CLIENT_TOKEN
