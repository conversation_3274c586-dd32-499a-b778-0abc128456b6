import type { Pool } from 'types/api/pools';

export const base: Pool = {
  pool_id: '******************************************',
  is_contract: true,
  chain_id: '1',
  base_token_address: '******************************************',
  base_token_symbol: 'USDT',
  base_token_icon_url: 'https://localhost:3000/utia.jpg',
  quote_token_address: '******************************************',
  quote_token_symbol: 'WETH',
  quote_token_icon_url: 'https://localhost:3000/secondary_utia.jpg',
  base_token_fully_diluted_valuation_usd: '75486579078',
  base_token_market_cap_usd: '139312819076.195',
  quote_token_fully_diluted_valuation_usd: '486579078',
  quote_token_market_cap_usd: '312819076.195',
  liquidity: '2099941.2238',
  dex: { id: 'sushiswap', name: '<PERSON>shi<PERSON>wa<PERSON>' },
  fee: '0.03',
  coin_gecko_terminal_url: 'https://www.geckoterminal.com/eth/pools/******************************************',
};

export const noIcons: Pool = {
  ...base,
  base_token_icon_url: null,
  quote_token_icon_url: null,
};
