import type { SmartContractSecurityAudits } from 'types/api/contract';

export const contractAudits: SmartContractSecurityAudits = {
  items: [
    {
      audit_company_name: 'OpenZeppelin',
      audit_publish_date: '2023-03-01',
      audit_report_url: 'https://blog.openzeppelin.com/eip-4337-ethereum-account-abstraction-incremental-audit',
    },
    {
      audit_company_name: 'OpenZeppelin',
      audit_publish_date: '2023-03-01',
      audit_report_url: 'https://blog.openzeppelin.com/eip-4337-ethereum-account-abstraction-incremental-audit',
    },
  ],
};
