import * as zetaChainCCTXType from '@blockscout/zetachain-cctx-types';

export const zetaChainCCTXItem: zetaChainCCTXType.CctxListItem = {
  index: '0xaea405aa63353312727dcc471e3242d3b8de0a181d6e35fe905fff4084bd3cc1',
  status: zetaChainCCTXType.CctxStatus.PENDING_OUTBOUND,
  status_reduced: zetaChainCCTXType.CctxStatusReduced.PENDING,
  amount: '185354164223052',
  source_chain_id: 7001,
  target_chain_id: 11155111,
  created_timestamp: 1641139800,
  last_update_timestamp: 1641139818,
  sender_address: '0xbE8b5d82DDE00677cCdb9dc22071CF635d459223',
  receiver_address: '0xcd54C6C6daEF72B04747F10A853a10c9Bef63286',
  asset: '0x1234567890123456789012345678901234567890',
  coin_type: zetaChainCCTXType.CoinType.ERC20,
  token_symbol: 'USDT.ARBSEP',
};

export const zetaChainCCTX: zetaChainCCTXType.CrossChainTx = {
  creator: '',
  index: '0x1c1e7410d7dfefe6173cc11efa47221e85587d3831c69108121198e0b2a86657',
  zeta_fees: '0',
  relayed_message: '0x0000000000000000000000000000000000000000000000000000000000000000',
  cctx_status_reduced: zetaChainCCTXType.CctxStatusReduced.SUCCESS,
  token_symbol: 'USDT.ARBSEP',
  token_name: 'USDT.ARBSEP',
  zrc20_contract_address: '0x1234567890123456789012345678901234567890',
  icon_url: 'https://example.com/icon.png',
  decimals: 6,
  cctx_status: {
    status: zetaChainCCTXType.CctxStatus.OUTBOUND_MINED,
    status_message: '',
    error_message: '',
    last_update_timestamp: 1641139818,
    is_abort_refunded: false,
    created_timestamp: 0,
    error_message_revert: '',
    error_message_abort: '',
  },
  inbound_params: {
    sender: '0x44D1F1f9289DBA1Cf5824bd667184cEBE020aA1c',
    sender_chain_id: 7001,
    tx_origin: '0xcf558D29999C119425d28bF1c07ba97FfF39e387',
    coin_type: zetaChainCCTXType.CoinType.GAS,
    asset: '',
    amount: '434880247204065094',
    observed_hash: '0x0001150419abd8d8383fae702f3a8415e57c96e78c9815756d15e1f1f5c0f466',
    observed_external_height: 1345648,
    ballot_index: '0x1c1e7410d7dfefe6173cc11efa47221e85587d3831c69108121198e0b2a86657',
    finalized_zeta_height: 0,
    tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.NOT_FINALIZED,
    is_cross_chain_call: false,
    status: zetaChainCCTXType.InboundStatus.INBOUND_SUCCESS,
    confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
  },
  outbound_params: [
    {
      receiver: '0xcf558D29999C119425d28bF1c07ba97FfF39e387',
      receiver_chain_id: 11155111,
      coin_type: zetaChainCCTXType.CoinType.GAS,
      amount: '434880247204065094',
      tss_nonce: 62305,
      gas_limit: 21000,
      gas_price: '4000000000',
      gas_priority_fee: '',
      hash: '0xe209ab8ee452d5ee3bc98e5035d33eb4d3f314880f8c956be2c0f78a26bfc37d',
      ballot_index: '0x9c5cc2a0ad2abba365105f1414ea261153634a5882bede9a883e0bb4f982cc55',
      observed_external_height: 32787262,
      gas_used: 0,
      effective_gas_price: '0',
      effective_gas_limit: 0,
      tss_pubkey: '',
      tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.NOT_FINALIZED,
      call_options: {
        gas_limit: 0,
        is_arbitrary_call: false,
      },
      confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
    },
  ],
  protocol_contract_version: zetaChainCCTXType.ProtocolContractVersion.V1,
  related_cctxs: [
    {
      index: '0x0001150419abd8d8383fae702f3a8415e57c96e78c9815756d15e1f1f5c0f466',
      depth: 0,
      source_chain_id: 7001,
      status: zetaChainCCTXType.CctxStatus.OUTBOUND_MINED,
      inbound_amount: '100000000000000000',
      inbound_coin_type: zetaChainCCTXType.CoinType.GAS,
      created_timestamp: 0,
      outbound_params: [
        { amount: '0', chain_id: 7001, coin_type: zetaChainCCTXType.CoinType.GAS, gas_used: 0 },
      ],
      status_reduced: zetaChainCCTXType.CctxStatusReduced.SUCCESS,
    },
  ],
};

export const zetaChainCCTXFailed: zetaChainCCTXType.CrossChainTx = {
  creator: 'zeta18pksjzclks34qkqyaahf2rakss80mnusju77cm',
  index: '0x004d60b58cbbead6ddc0a100e17b88484f72e4f47b10c4b560a41cadd3315c4c',
  zeta_fees: '0',
  // eslint-disable-next-line max-len
  relayed_message: '00000000000000000000000000000000000000000000000000000000000000400000000000000000000000006b513b40ebc0b4d7b197730476ed3324346f28220000000000000000000000000000000000000000000000000000000000000014dcbfa87533a478743b3f507e76170ea6f26fa69a000000000000000000000000',
  cctx_status_reduced: zetaChainCCTXType.CctxStatusReduced.FAILED,
  token_symbol: 'UPKRW.SEPOLIA',
  token_name: 'ZetaChain ZRC20 UPKRW on Sepolia',
  zrc20_contract_address: '******************************************',
  decimals: 6,
  cctx_status: {
    status: zetaChainCCTXType.CctxStatus.REVERTED,
    status_message: '',
    // eslint-disable-next-line max-len
    error_message: '{"type":"contract_call_error","message":"contract call failed when calling EVM with data","error":"execution reverted: ret 0x: evm transaction execution failed","method":"depositAndCall0","contract":"******************************************","args":"[{[220 191 168 117 51 164 120 116 59 63 80 126 118 23 14 166 242 111 166 154] ****************************************** 43113} ****************************************** 10000000 ****************************************** [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 64 0 0 0 0 0 0 0 0 0 0 0 0 107 81 59 64 235 192 180 215 177 151 115 4 118 237 51 36 52 111 40 34 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 20 220 191 168 117 51 164 120 116 59 63 80 126 118 23 14 166 242 111 166 154 0 0 0 0 0 0 0 0 0 0 0 0]]"}',
    last_update_timestamp: 1641139818,
    is_abort_refunded: false,
    created_timestamp: 1641139810,
    error_message_revert: '',
    error_message_abort: '',
  },
  inbound_params: {
    sender: '******************************************',
    sender_chain_id: 7001,
    tx_origin: '******************************************',
    coin_type: zetaChainCCTXType.CoinType.ERC20,
    asset: '0x6B513B40eBc0B4D7B197730476ed3324346F2822',
    amount: '10000000',
    observed_hash: '0x8ecc5913b637b2756258e8155e2b22dc5afa820698163c1627d372e187e6e65b',
    observed_external_height: 45237981,
    ballot_index: '0x004d60b58cbbead6ddc0a100e17b88484f72e4f47b10c4b560a41cadd3315c4c',
    finalized_zeta_height: 12324831,
    tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.EXECUTED,
    is_cross_chain_call: true,
    status: zetaChainCCTXType.InboundStatus.INBOUND_SUCCESS,
    confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
  },
  outbound_params: [
    {
      receiver: '******************************************',
      receiver_chain_id: 11155111,
      coin_type: zetaChainCCTXType.CoinType.ERC20,
      amount: '0',
      tss_nonce: 0,
      gas_limit: 0,
      gas_price: '',
      gas_priority_fee: '',
      hash: '0x3c426a57742fb55338fbb79318298cc18ca1778ba83196993548921badbe40bb',
      ballot_index: '',
      observed_external_height: 12324831,
      gas_used: 0,
      effective_gas_price: '0',
      effective_gas_limit: 0,
      tss_pubkey: 'zetapub1addwnpepq28c57cvcs0a2htsem5zxr6qnlvq9mzhmm76z3jncsnzz32rclangr2g35p',
      tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.EXECUTED,
      call_options: {
        gas_limit: 1500000,
        is_arbitrary_call: false,
      },
      confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
    },
    {
      receiver: '******************************************',
      receiver_chain_id: 11155111,
      coin_type: zetaChainCCTXType.CoinType.ERC20,
      amount: '9999999',
      tss_nonce: 842,
      gas_limit: 0,
      gas_price: '2',
      gas_priority_fee: '0',
      hash: '0xa17a2ecf87ac2527f49751d487ef20cb44de8965a6bc3817cd188705744a2041',
      ballot_index: '',
      observed_external_height: 0,
      gas_used: 61955,
      effective_gas_price: '2',
      effective_gas_limit: 100000,
      tss_pubkey: 'zetapub1addwnpepq28c57cvcs0a2htsem5zxr6qnlvq9mzhmm76z3jncsnzz32rclangr2g35p',
      tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.EXECUTED,
      call_options: {
        gas_limit: 21000,
        is_arbitrary_call: false,
      },
      confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
    },
  ],
  protocol_contract_version: zetaChainCCTXType.ProtocolContractVersion.V2,
  revert_options: {
    revert_address: '0x0000000000000000000000000000000000000000',
    call_on_revert: false,
    abort_address: '0x0000000000000000000000000000000000000000',
    // eslint-disable-next-line max-len
    revert_message: 'QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBRUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBWUFBQUFBQUFBQUFBQUFBQUFGQjFQS05KWTJ5b2N5ZGk2TXp3VjlPWm1KR2dBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUNob2RIUndjem92TDJGd2FTNWxlR0Z0Y0d4bExtTnZiUzl0WlhSaFpHRjBZUzh2TVM1cWMyOXVBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQ==',
    revert_gas_limit: '0',
  },
  related_cctxs: [
    {
      index: '0x004d60b58cbbead6ddc0a100e17b88484f72e4f47b10c4b560a41cadd3315c4c',
      depth: 0,
      source_chain_id: 7001,
      status: zetaChainCCTXType.CctxStatus.REVERTED,
      status_reduced: zetaChainCCTXType.CctxStatusReduced.FAILED,
      inbound_amount: '10000000',
      inbound_coin_type: zetaChainCCTXType.CoinType.ERC20,
      outbound_params: [
        {
          amount: '0',
          chain_id: 7001,
          coin_type: zetaChainCCTXType.CoinType.ERC20,
          gas_used: 0,
        },
        {
          amount: '9999999',
          chain_id: 11155111,
          coin_type: zetaChainCCTXType.CoinType.ERC20,
          gas_used: 61955,
        },
      ],
      token_symbol: 'UPKRW.SEPOLIA',
      token_name: 'ZetaChain ZRC20 UPKRW on Sepolia',
      token_decimals: 6,
      token_zrc20_contract_address: '******************************************',
      created_timestamp: 1641139810,
    },
  ],
};

export const zetaChainCCTXPending: zetaChainCCTXType.CrossChainTx = {
  ...zetaChainCCTX,
  index: '0x5f5f7410d7dfefe6173cc11efa47221e85587d3831c69108121198e0b2a86661',
  cctx_status_reduced: zetaChainCCTXType.CctxStatusReduced.PENDING,
  cctx_status: {
    ...zetaChainCCTX.cctx_status,
    status: zetaChainCCTXType.CctxStatus.PENDING_INBOUND,
    status_message: 'Waiting for inbound confirmation',
    error_message: '',
    last_update_timestamp: 1641139818,
    is_abort_refunded: false,
    created_timestamp: 1641139810,
    error_message_revert: '',
    error_message_abort: '',
  },
  outbound_params: [],
};

export const zetaChainCCTXList: zetaChainCCTXType.ListCctxsResponse = {
  items: [
    zetaChainCCTXItem,
    {
      ...zetaChainCCTXItem,
      index: '0x2d2e7410d7dfefe6173cc11efa47221e85587d3831c69108121198e0b2a86658',
      status: zetaChainCCTXType.CctxStatus.OUTBOUND_MINED,
      status_reduced: zetaChainCCTXType.CctxStatusReduced.SUCCESS,
      amount: '500000000000000000',
      sender_address: '0x1234567890123456789012345678901234567890',
      receiver_address: '0xabcdefabcdefabcdefabcdefabcdefabcdefabcd',
    },
    {
      ...zetaChainCCTXItem,
      index: '0x3f3f7410d7dfefe6173cc11efa47221e85587d3831c69108121198e0b2a86659',
      status: zetaChainCCTXType.CctxStatus.PENDING_INBOUND,
      status_reduced: zetaChainCCTXType.CctxStatusReduced.PENDING,
      amount: '750000000000000000',
      sender_address: '0xabcdefabcdefabcdefabcdefabcdefabcdefabcd',
      receiver_address: '0x9876543210987654321098765432109876543210',
    },
  ],
  next_page_params: { page_key: 1, limit: 50, direction: zetaChainCCTXType.Direction.DESC },
};
