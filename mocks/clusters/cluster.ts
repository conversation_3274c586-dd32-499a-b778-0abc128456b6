import type { ClusterByNameResponse, ClusterByIdResponse } from 'types/api/clusters';

export const campNetworkClusterByName: ClusterByNameResponse = {
  result: {
    data: {
      name: 'campnetwork/lol',
      owner: '******************************************',
      clusterId: 'clstr_1a2b3c4d5e6f7g8h9i0j',
      backingWei: '5000000000000000000',
      expiresAt: '2025-01-15T10:30:00Z',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:22:00Z',
      updatedBy: '******************************************',
      isTestnet: false,
    },
  },
};

export const duckClusterByName: ClusterByNameResponse = {
  result: {
    data: {
      name: 'duck/quack',
      owner: '0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef',
      clusterId: 'clstr_9z8y7x6w5v4u3t2s1r0q',
      backingWei: '12000000000000000000',
      expiresAt: null,
      createdAt: '2024-02-01T08:15:00Z',
      updatedAt: '2024-02-05T16:45:00Z',
      updatedBy: '0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef',
      isTestnet: false,
    },
  },
};

export const campNetworkClusterById: ClusterByIdResponse = {
  result: {
    data: {
      id: 'clstr_1a2b3c4d5e6f7g8h9i0j',
      createdBy: '******************************************',
      createdAt: '2024-01-15T10:30:00Z',
      wallets: [
        {
          address: '******************************************',
          name: 'main.campnetwork',
          chainIds: [ '1', '137' ],
        },
        {
          address: '******************************************',
          name: 'treasury.campnetwork',
          chainIds: [ '1' ],
        },
        {
          address: '******************************************',
          name: 'staking.campnetwork',
          chainIds: [ '137', '56' ],
        },
      ],
      isTestnet: false,
    },
  },
};

export const testnetClusterByName: ClusterByNameResponse = {
  result: {
    data: {
      name: 'test/cluster',
      owner: '******************************************',
      clusterId: 'clstr_test123456789',
      backingWei: '1000000000000000000',
      expiresAt: '2024-12-31T23:59:59Z',
      createdAt: '2024-03-01T12:00:00Z',
      updatedAt: '2024-03-01T12:00:00Z',
      updatedBy: '******************************************',
      isTestnet: true,
    },
  },
};
