/* eslint-disable max-len */
export const apps = [
  {
    author: 'Hop',
    id: 'hop-exchange',
    title: 'Hop',
    logo: 'https://www.gitbook.com/cdn-cgi/image/width=288,dpr=2.200000047683716,format=auto/https%3A%2F%2Ffiles.gitbook.com%2Fv0%2Fb%2Fgitbook-x-prod.appspot.com%2Fo%2Fspaces%252F-Lq1XoWGmy8zggj_u2fM%252Fuploads%252FfhJJGsR3RSfFmRoxfvqk%252FHop.png%3Falt%3Dmedia%26token%3D8107e45c-546c-4771-bbfe-e86bb0fe8c1a',
    categories: [ 'Bridge' ],
    shortDescription: 'Hop is a scalable rollup-to-rollup general token bridge. It allows users to send tokens from one rollup or sidechain to another almost immediately without having to wait for the networks challenge period.',
    site: 'https://help.hop.exchange/hc/en-us/articles/4405172445197-What-is-Hop-Protocol-',
    description: 'Hop is a scalable rollup-to-rollup general token bridge. It allows users to send tokens from one rollup or sidechain to another almost immediately without having to wait for the networks challenge period.',
    external: true,
    url: 'https://goerli.hop.exchange/send?token=ETH&sourceNetwork=ethereum',
    github: [ 'https://github.com/hop-protocol/hop', 'https://github.com/hop-protocol/hop-ui' ],
    discord: 'https://discord.gg/hopprotocol',
    twitter: 'https://twitter.com/HopProtocol',
    rating: 4.3,
    ratingsTotalCount: 15,
  },
  {
    author: 'Blockscout',
    id: 'token-approval-tracker',
    title: 'Token Approval Tracker',
    logo: 'https://approval-tracker.apps.blockscout.com/icon-192.png',
    categories: [ 'Infra & Dev tooling' ],
    shortDescription: 'Token Approval Tracker shows all approvals for any ERC20-compliant tokens and NFTs and lets to revoke them or adjust the approved amount.',
    site: 'https://docs.blockscout.com/for-users/blockscout-apps/token-approval-tracker',
    description: 'Token Approval Tracker shows all approvals for any ERC20-compliant tokens and NFTs and lets to revoke them or adjust the approved amount.',
    url: 'https://approval-tracker.apps.blockscout.com/',
  },
];
