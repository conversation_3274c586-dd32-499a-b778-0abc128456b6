import type { UserInfo } from 'types/api/account';

export const base: UserInfo = {
  avatar: 'https://avatars.githubusercontent.com/u/********',
  email: '<EMAIL>',
  name: 'tom goriunov',
  nickname: 'tom2drum',
  address_hash: null,
};

export const withoutEmail: UserInfo = {
  avatar: 'https://avatars.githubusercontent.com/u/********',
  email: null,
  name: 'tom goriunov',
  nickname: 'tom2drum',
  address_hash: '******************************************',
};

export const withEmailAndWallet: UserInfo = {
  avatar: 'https://avatars.githubusercontent.com/u/********',
  email: '<EMAIL>',
  name: 'tom goriunov',
  nickname: 'tom2drum',
  address_hash: '******************************************',
};
