import type { Route } from 'nextjs-routes';

export const PAGE_TYPE_DICT: Record<Route['pathname'], string> = {
  '/': 'Homepage',
  '/txs': 'Transactions',
  '/internal-txs': 'Internal transactions',
  '/txs/kettle/[hash]': 'Kettle transactions',
  '/tx/[hash]': 'Transaction details',
  '/blocks': 'Blocks',
  '/block/[height_or_hash]': 'Block details',
  '/block/countdown': 'Block countdown search',
  '/block/countdown/[height]': 'Block countdown',
  '/accounts': 'Top accounts',
  '/accounts/label/[slug]': 'Addresses search by label',
  '/address/[hash]': 'Address details',
  '/verified-contracts': 'Verified contracts',
  '/contract-verification': 'Contract verification',
  '/address/[hash]/contract-verification': 'Contract verification for address',
  '/tokens': 'Tokens',
  '/token/[hash]': 'Token details',
  '/token/[hash]/instance/[id]': 'Token Instance',
  '/apps': 'DApps',
  '/apps/[id]': 'DApp',
  '/clusters/[name]': 'Cluster details',
  '/stats': 'Stats',
  '/stats/[id]': 'Stats chart',
  '/uptime': 'Uptime',
  '/api-docs': 'REST API',
  '/search-results': 'Search results',
  '/auth/profile': 'Profile',
  '/account/merits': 'Merits',
  '/account/watchlist': 'Watchlist',
  '/account/api-key': 'API keys',
  '/account/custom-abi': 'Custom ABI',
  '/account/tag-address': 'Private tags',
  '/account/verified-addresses': 'Verified addresses',
  '/public-tags/submit': 'Submit public tag',
  '/withdrawals': 'Withdrawals',
  '/txn-withdrawals': 'Txn withdrawals',
  '/visualize/sol2uml': 'Solidity UML diagram',
  '/csv-export': 'Export data to CSV file',
  '/deposits': 'Deposits',
  '/output-roots': 'Output roots',
  '/dispute-games': 'Dispute games',
  '/batches': 'Txn batches',
  '/batches/[number]': 'L2 txn batch details',
  '/batches/celestia/[height]/[commitment]': 'L2 txn batch details',
  '/blobs/[hash]': 'Blob details',
  '/ops': 'User operations',
  '/op/[hash]': 'User operation details',
  '/404': '404',
  '/name-domains': 'Domains search and resolve',
  '/name-domains/[name]': 'Domain details',
  '/validators': 'Validators list',
  '/validators/[id]': 'Validator details',
  '/epochs': 'Epochs',
  '/epochs/[number]': 'Epoch details',
  '/gas-tracker': 'Gas tracker',
  '/mud-worlds': 'MUD worlds',
  '/token-transfers': 'Token transfers',
  '/advanced-filter': 'Advanced filter',
  '/pools': 'DEX pools',
  '/pools/[hash]': 'Pool details',
  '/interop-messages': 'Interop messages',
  '/operations': 'Operations',
  '/operation/[id]': 'Operation details',
  '/cc/tx/[hash]': 'Cross-chain transaction details',

  // multichain routes
  '/chain/[chain-slug]/accounts/label/[slug]': 'Chain addresses search by label',
  '/chain/[chain-slug]/advanced-filter': 'Chain advanced filter',
  '/chain/[chain-slug]/block/[height_or_hash]': 'Chain block details',
  '/chain/[chain-slug]/block/countdown': 'Chain block countdown index',
  '/chain/[chain-slug]/block/countdown/[height]': 'Chain block countdown',
  '/chain/[chain-slug]/csv-export': 'Chain export data to CSV',
  '/chain/[chain-slug]/op/[hash]': 'Chain user operation details',
  '/chain/[chain-slug]/token/[hash]': 'Chain token details',
  '/chain/[chain-slug]/token/[hash]/instance/[id]': 'Chain token NFT instance',
  '/chain/[chain-slug]/tx/[hash]': 'Chain transaction details',

  // service routes, added only to make typescript happy
  '/login': 'Login',
  '/sprite': 'Sprite',
  '/chakra': 'Chakra UI showcase',
  '/api/metrics': 'Node API: Prometheus metrics',
  '/api/monitoring/invalid-api-schema': 'Node API: Prometheus metrics',
  '/api/log': 'Node API: Request log',
  '/api/media-type': 'Node API: Media type',
  '/api/proxy': 'Node API: Proxy',
  '/api/csrf': 'Node API: CSRF token',
  '/api/healthz': 'Node API: Health check',
  '/api/config': 'Node API: App config',
};

export default function getPageType(pathname: Route['pathname']) {
  return PAGE_TYPE_DICT[pathname] || 'Unknown page';
}
