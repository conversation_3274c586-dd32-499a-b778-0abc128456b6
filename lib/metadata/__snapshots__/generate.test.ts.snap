// Jest <PERSON>nap<PERSON> v1, https://goo.gl/fbAQLP

exports[`generates correct metadata for: dynamic route 1`] = `
{
  "canonical": undefined,
  "description": "View transaction 0x12345 on Blockscout (Blockscout) Explorer",
  "opengraph": {
    "description": "",
    "imageUrl": "",
    "title": "Blockscout transaction 0x12345 | Blockscout",
  },
  "title": "Blockscout transaction 0x12345 | Blockscout",
}
`;

exports[`generates correct metadata for: dynamic route with API data 1`] = `
{
  "canonical": undefined,
  "description": "0x12345, balances and analytics on the Blockscout (Blockscout) Explorer",
  "opengraph": {
    "description": "",
    "imageUrl": "",
    "title": "Blockscout USDT token details | Blockscout",
  },
  "title": "Blockscout USDT token details | Blockscout",
}
`;

exports[`generates correct metadata for: static route 1`] = `
{
  "canonical": "http://localhost:3000/txs",
  "description": "Open-source block explorer by Blockscout. Search transactions, verify smart contracts, analyze addresses, and track network activity. Complete blockchain data and APIs for the Blockscout (Blockscout) Explorer network.",
  "opengraph": {
    "description": "",
    "imageUrl": "http://localhost:3000/static/og_image.png",
    "title": "Blockscout transactions - Blockscout explorer | Blockscout",
  },
  "title": "Blockscout transactions - Blockscout explorer | Blockscout",
}
`;
