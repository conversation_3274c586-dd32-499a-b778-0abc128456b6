// This file is generated by npm run build:icons
  
  export type IconName =
  	| "ABI_slim"
	| "ABI"
	| "advanced-filter"
	| "API_slim"
	| "API"
	| "apps_list"
	| "apps_slim"
	| "apps"
	| "arrows/down-right"
	| "arrows/east-mini"
	| "arrows/east"
	| "arrows/north-east"
	| "arrows/south-east"
	| "arrows/up-down"
	| "arrows/up-head"
	| "beta_xs"
	| "beta"
	| "blob"
	| "blobs/image"
	| "blobs/raw"
	| "blobs/text"
	| "block_countdown"
	| "block_slim"
	| "block"
	| "brands/blockscout"
	| "brands/celenium"
	| "brands/graph"
	| "brands/safe"
	| "brands/solidity_scan"
	| "brands/tac"
	| "brands/ton"
	| "burger"
	| "certified"
	| "check"
	| "checkered_flag"
	| "clock-light"
	| "clock"
	| "close"
	| "clusters"
	| "coins/bitcoin"
	| "collection"
	| "columns"
	| "contracts/proxy"
	| "contracts/regular_many"
	| "contracts/regular"
	| "contracts/verified_many"
	| "contracts/verified"
	| "copy_check"
	| "copy"
	| "cross"
	| "delete"
	| "dex-tracker"
	| "docs"
	| "donate"
	| "dots"
	| "edit"
	| "email"
	| "empty_search_result"
	| "ENS_slim"
	| "ENS"
	| "error-pages/403"
	| "error-pages/404"
	| "error-pages/422"
	| "error-pages/429"
	| "error-pages/500"
	| "explorer"
	| "files/csv"
	| "files/image"
	| "files/json"
	| "files/placeholder"
	| "files/sol"
	| "files/yul"
	| "filter"
	| "flame"
	| "flashblock"
	| "games"
	| "gas_xl"
	| "gas"
	| "gear_slim"
	| "gear"
	| "globe-b"
	| "globe"
	| "graph"
	| "heart_filled"
	| "heart_outline"
	| "hexagon"
	| "hourglass_slim"
	| "hourglass"
	| "info_filled"
	| "info"
	| "integration/full"
	| "integration/partial"
	| "internal_txns"
	| "interop_slim"
	| "interop"
	| "key"
	| "lightning_navbar"
	| "lightning"
	| "link_external"
	| "link"
	| "lock"
	| "merits_colored"
	| "merits_slim"
	| "merits_with_dot_slim"
	| "merits_with_dot"
	| "merits"
	| "minus"
	| "monaco/cargo"
	| "monaco/file"
	| "monaco/folder-open"
	| "monaco/folder"
	| "monaco/rust"
	| "monaco/solidity"
	| "monaco/toml"
	| "monaco/vyper"
	| "moon-with-star"
	| "moon"
	| "MUD_menu"
	| "MUD"
	| "networks"
	| "networks/icon-placeholder"
	| "networks/logo-placeholder"
	| "nft_shield"
	| "open-link"
	| "operation_slim"
	| "operation"
	| "output_roots"
	| "payment_link"
	| "pie_chart"
	| "plus"
	| "private_tags_slim"
	| "privattags"
	| "profile"
	| "publictags_slim"
	| "publictags"
	| "qr_code"
	| "refresh_menu"
	| "refresh"
	| "repeat"
	| "restAPI"
	| "rocket_xl"
	| "rocket"
	| "RPC"
	| "scope"
	| "score/score-not-ok"
	| "score/score-ok"
	| "search"
	| "share"
	| "sign_out"
	| "social/coingecko"
	| "social/coinmarketcap"
	| "social/defi_llama"
	| "social/discord_filled"
	| "social/discord"
	| "social/facebook_filled"
	| "social/git"
	| "social/github_filled"
	| "social/linkedin_filled"
	| "social/medium_filled"
	| "social/opensea_filled"
	| "social/reddit_filled"
	| "social/slack_filled"
	| "social/stats"
	| "social/telega"
	| "social/telegram_filled"
	| "social/twitter_filled"
	| "social/twitter"
	| "star_filled"
	| "star_outline"
	| "stats"
	| "status/error"
	| "status/pending"
	| "status/success"
	| "status/warning"
	| "sun"
	| "swap"
	| "token-placeholder"
	| "token-transfers"
	| "token"
	| "tokens"
	| "tokens/xdai"
	| "top-accounts"
	| "transactions_slim"
	| "transactions"
	| "txn_batches_slim"
	| "txn_batches"
	| "uniswap"
	| "user_op_slim"
	| "user_op"
	| "validator"
	| "verification-steps/error"
	| "verification-steps/finalized"
	| "verification-steps/unfinalized"
	| "verified_slim"
	| "verified"
	| "wallet"
	| "wallets/coinbase"
	| "wallets/metamask"
	| "wallets/token-pocket"
	| "watchlist";
  