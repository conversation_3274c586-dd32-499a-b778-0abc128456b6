import type { NovesResponseData, NovesClassificationData, NovesRawTransactionData } from 'types/api/noves';

const NOVES_TRANSLATE_CLASSIFIED: NovesClassificationData = {
  description: 'Sent 0.04 ETH',
  received: [ {
    action: 'Sent Token',
    actionFormatted: 'Sent Token',
    amount: '45',
    from: { name: '', address: '******************************************' },
    to: { name: '', address: '******************************************' },
    token: {
      address: '',
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18,
    },
  } ],
  sent: [],
  source: {
    type: '',
  },
  type: '0x2',
  typeFormatted: 'Send NFT',
};

const NOVES_TRANSLATE_RAW: NovesRawTransactionData = {
  blockNumber: 1,
  fromAddress: '******************************************',
  gas: 2,
  gasPrice: 3,
  timestamp: 20000,
  toAddress: '******************************************',
  transactionFee: 2,
  transactionHash: '******************************************',
};

export const NOVES_TRANSLATE: NovesResponseData = {
  accountAddress: '0x2b824349b320cfa72f292ab26bf525adb00083ba9fa097141896c3c8c74567cc',
  chain: 'base',
  txTypeVersion: 2,
  rawTransactionData: NOVES_TRANSLATE_RAW,
  classificationData: NOVES_TRANSLATE_CLASSIFIED,
};
