import * as zetaChainCCTXType from '@blockscout/zetachain-cctx-types';

import { ADDRESS_HASH } from './addressParams';
import { BLOCK_HASH } from './block';
import { TX_HASH } from './tx';

export const ZETA_CHAIN_CCTX: zetaChainCCTXType.CrossChainTx = {
  creator: ADDRESS_HASH,
  index: TX_HASH,
  zeta_fees: '0',
  relayed_message: '',
  cctx_status_reduced: zetaChainCCTXType.CctxStatusReduced.PENDING,
  token_symbol: 'USDT.ARBSEP',
  token_name: 'USDT.ARBSEP',
  zrc20_contract_address: ADDRESS_HASH,
  decimals: 6,
  cctx_status: {
    status: zetaChainCCTXType.CctxStatus.OUTBOUND_MINED,
    status_message: '',
    error_message: '',
    last_update_timestamp: 1641139818,
    is_abort_refunded: false,
    created_timestamp: 1641139810,
    error_message_revert: '',
    error_message_abort: '',
  },
  inbound_params: {
    sender: ADDRESS_HASH,
    sender_chain_id: 7001,
    tx_origin: ADDRESS_HASH,
    coin_type: zetaChainCCTXType.CoinType.GAS,
    asset: '',
    amount: '434880247204065094',
    observed_hash: BLOCK_HASH,
    observed_external_height: 12324831,
    ballot_index: '',
    finalized_zeta_height: 0,
    tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.NOT_FINALIZED,
    is_cross_chain_call: false,
    status: zetaChainCCTXType.InboundStatus.INBOUND_SUCCESS,
    confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
  },
  outbound_params: [
    {
      receiver: ADDRESS_HASH,
      receiver_chain_id: 7001,
      coin_type: zetaChainCCTXType.CoinType.GAS,
      amount: '0',
      tss_nonce: 0,
      gas_limit: 0,
      gas_price: '',
      gas_priority_fee: '',
      hash: '',
      ballot_index: '',
      observed_external_height: 0,
      gas_used: 0,
      effective_gas_price: '',
      effective_gas_limit: 0,
      tss_pubkey: '',
      tx_finalization_status: zetaChainCCTXType.TxFinalizationStatus.NOT_FINALIZED,
      call_options: {
        gas_limit: 0,
        is_arbitrary_call: false,
      },
      confirmation_mode: zetaChainCCTXType.ConfirmationMode.SAFE,
    },
  ],
  protocol_contract_version: zetaChainCCTXType.ProtocolContractVersion.V1,
  revert_options: {
    revert_address: ADDRESS_HASH,
    call_on_revert: false,
    abort_address: ADDRESS_HASH,
    revert_message: '',
    revert_gas_limit: '0',
  },
  related_cctxs: [],
};

export const ZETA_CHAIN_CCTX_LIST_ITEM: zetaChainCCTXType.CctxListItem = {
  index: TX_HASH,
  status: zetaChainCCTXType.CctxStatus.OUTBOUND_MINED,
  status_reduced: zetaChainCCTXType.CctxStatusReduced.SUCCESS,
  amount: '434880247204065094',
  source_chain_id: 7001,
  target_chain_id: 7001,
  created_timestamp: 1641139810,
  last_update_timestamp: 1641139818,
  sender_address: ADDRESS_HASH,
  receiver_address: ADDRESS_HASH,
  asset: '',
  coin_type: zetaChainCCTXType.CoinType.GAS,
  token_symbol: 'USDT.ARBSEP',
  decimals: 6,
};
