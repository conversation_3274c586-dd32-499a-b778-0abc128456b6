name: Publish Docker image

on:
    workflow_dispatch:
        inputs:
            tags:
                description: Image tags (e.g. "type=raw,value=foo")
                required: false
                type: string
            build_args:
                description: Build-time variables
                required: false
                type: string
            platforms:
                description: Image platforms (you can specify multiple platforms separated by comma)
                required: false
                type: string
                default: linux/amd64,linux/arm64/v8
            private:
                description: Make image private (true) or public (false)
                required: false
                type: boolean
                default: true
    workflow_call:
        inputs:
            tags:
                description: Image tags (e.g. "type=raw,value=foo")
                required: false
                type: string
            build_args:
                description: Build-time variables
                required: false
                type: string
            platforms:
                description: Image platforms (you can specify multiple platforms separated by comma)
                required: false
                type: string
                default: linux/amd64,linux/arm64/v8
            private:
                description: Make image private (true) or public (false)
                required: false
                type: boolean
                default: true

permissions:
    contents: read
    packages: write

jobs:
    run:
        name: Run
        runs-on: ubuntu-latest
        steps:
            -   name: Check out the repo
                uses: actions/checkout@v4
    
            -   name: Set image name based on private flag
                id: image-name
                run: |
                    if [ "${{ inputs.private }}" = "true" ]; then
                        echo "image-name=ghcr.io/blockscout/frontend-private" >> $GITHUB_OUTPUT
                    else
                        echo "image-name=ghcr.io/blockscout/frontend" >> $GITHUB_OUTPUT
                    fi
    
            # Will automatically make nice tags, see the table here https://github.com/docker/metadata-action#basic
            -   name: Docker meta
                id: meta
                uses: docker/metadata-action@v5
                with:
                    images: ${{ steps.image-name.outputs.image-name }}
                    flavor: |
                        latest=false
                    tags: |
                        type=ref,event=tag
                        ${{ inputs.tags }}
    
            -   name: Add SHORT_SHA env property with commit short sha
                run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-8`" >> $GITHUB_ENV

            -   name: Debug
                env:
                    REF_TYPE: ${{ github.ref_type }}
                    REF_NAME: ${{ github.ref_name }}
                    IMAGE_NAME: ${{ steps.image-name.outputs.image-name }}
                    IS_PRIVATE: ${{ inputs.private }}
                run: |
                    echo "ref_type: $REF_TYPE"
                    echo "ref_name: $REF_NAME"
                    echo "image_name: $IMAGE_NAME"
                    echo "is_private: $IS_PRIVATE"
    
            -   name: Setup repo
                uses: blockscout/actions/.github/actions/setup-multiarch-buildx@no-metadata
                id: setup
                with:
                    docker-image: ${{ steps.image-name.outputs.image-name }}
                    docker-username: ${{ github.actor }}
                    docker-password: ${{ secrets.GITHUB_TOKEN }}
                    docker-remote-multi-platform: true
                    docker-arm-host: ${{ secrets.ARM_RUNNER_HOSTNAME }}
                    docker-arm-host-key: ${{ secrets.ARM_RUNNER_KEY }}

            -   name: Build and push
                uses: docker/build-push-action@v5
                with:
                    context: .
                    file: ./Dockerfile
                    push: true
                    cache-from: type=gha
                    tags: ${{ steps.meta.outputs.tags }}
                    platforms: ${{ inputs.platforms }}
                    labels: ${{ steps.meta.outputs.labels }}
                    build-args: |
                        GIT_COMMIT_SHA=${{ env.SHORT_SHA }}
                        GIT_TAG=${{ github.ref_type == 'tag' && github.ref_name || '' }}
                        ${{ inputs.build_args }}