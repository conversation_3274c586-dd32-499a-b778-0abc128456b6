# Set of ENVs for MEGA Testnet network explorer
# https://megaeth-testnet.blockscout.com
# This is an auto-generated file. To update all values, run "yarn dev:preset:sync --name=mega_eth"

# Local ENVs
NEXT_PUBLIC_APP_PROTOCOL=http
NEXT_PUBLIC_APP_HOST=localhost
NEXT_PUBLIC_APP_PORT=3000
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_API_WEBSOCKET_PROTOCOL=ws

NEXT_PUBLIC_MEGA_ETH_SOCKET_URL_METRICS=wss://testnet-dashboard.megaeth.com/metrics

# Instance ENVs
NEXT_PUBLIC_ADDRESS_3RD_PARTY_WIDGETS=['talentprotocol', 'efp', 'webacy', 'humanpassport', 'trustblock', 'smartmuv', 'blockscoutbadges', 'etherscore', 'gitpoap', 'drops', 'humanode', 'deepdao']
NEXT_PUBLIC_ADDRESS_3RD_PARTY_WIDGETS_CONFIG_URL=https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/widgets/config.json
NEXT_PUBLIC_ADMIN_SERVICE_API_HOST=https://admin-rs.services.blockscout.com
NEXT_PUBLIC_API_BASE_PATH=/
NEXT_PUBLIC_API_HOST=megaeth-testnet.blockscout.com
NEXT_PUBLIC_API_SPEC_URL=https://raw.githubusercontent.com/blockscout/blockscout-api-v2-swagger/main/swagger.yaml
NEXT_PUBLIC_COLOR_THEME_DEFAULT=dark
NEXT_PUBLIC_COLOR_THEME_OVERRIDES={'bg':{'primary':{'_light':{'value':'rgba(254,253,253)'},'_dark':{'value':'rgba(25,25,26)'}}},'text':{'primary':{'_light':{'value':'rgba(16,17,18,0.80)'},'_dark':{'value':'rgba(222,217,217)'}},'secondary':{'_light':{'value':'rgba(138,136,136)'},'_dark':{'value':'rgba(133,133,133)'}}},'hover':{'_light':{'value':'rgba(104,200,158)'},'_dark':{'value':'rgba(104,200,158)'}},'selected':{'control':{'text':{'_light':{'value':'rgba(25,25,26)'},'_dark':{'value':'rgba(247,250,252)'}},'bg':{'_light':{'value':'rgba(242,239,239)'},'_dark':{'value':'rgba(255,255,255,0.06)'}}},'option':{'bg':{'_light':{'value':'rgba(84,75,75)'},'_dark':{'value':'rgba(87,87,87)'}}}},'icon':{'primary':{'_light':{'value':'rgba(138,136,136)'},'_dark':{'value':'rgba(133,133,133)'}},'secondary':{'_light':{'value':'rgba(176,176,176)'},'_dark':{'value':'rgba(105,103,103)'}}},'button':{'primary':{'_light':{'value':'rgba(105,103,103)'},'_dark':{'value':'rgba(133,133,133)'}}},'link':{'primary':{'_light':{'value':'rgba(57,146,108)'},'_dark':{'value':'rgba(57,146,108)'}}},'graph':{'line':{'_light':{'value':'rgba(105,103,103)'},'_dark':{'value':'rgba(57,146,108)'}},'gradient':{'start':{'_light':{'value':'rgba(105,103,103,0.3)'},'_dark':{'value':'rgba(57,146,108,0.3)'}},'stop':{'_light':{'value':'rgba(105,103,103,0)'},'_dark':{'value':'rgba(57,146,108,0)'}}}},'stats':{'bg':{'_light':{'value':'rgba(242,239,239)'},'_dark':{'value':'rgba(255,255,255,0.06)'}}},'topbar':{'bg':{'_light':{'value':'rgba(242,239,239)'},'_dark':{'value':'rgba(255,255,255,0.06)'}}},'navigation':{'text':{'selected':{'_light':{'value':'rgba(25,25,26)'},'_dark':{'value':'rgba(247,250,252)'}}},'bg':{'selected':{'_light':{'value':'rgba(242,239,239)'},'_dark':{'value':'rgba(255,255,255,0.06)'}}}},'tabs':{'text':{'primary':{'_light':{'value':'rgba(25,25,26)'},'_dark':{'value':'rgba(222,217,217)'}}}}}
NEXT_PUBLIC_CONTRACT_CODE_IDES=[{'title':'Remix IDE','url':'https://remix.ethereum.org/?address={hash}&blockscout={domain}','icon_url':'https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/ide-icons/remix.png'}]
NEXT_PUBLIC_CONTRACT_INFO_API_HOST=https://contracts-info.services.blockscout.com
NEXT_PUBLIC_GAME_BADGE_CLAIM_LINK=https://badges.blockscout.com/mint/sherblockHolmesBadge
NEXT_PUBLIC_GRAPHIQL_TRANSACTION=1
NEXT_PUBLIC_HOMEPAGE_CHARTS=['daily_txs']
NEXT_PUBLIC_HOMEPAGE_HERO_BANNER_CONFIG={'background':['rgba(223,217,217,1)','transparent'],'text_color':['rgba(16,17,18,0.80)','rgba(222,217,217,1)'],'border':['none','1px solid rgba(105,103,103,1)'],'search':{'border_width':['0px','2px']}}
NEXT_PUBLIC_IS_TESTNET=true
NEXT_PUBLIC_METADATA_SERVICE_API_HOST=https://metadata.services.blockscout.com
NEXT_PUBLIC_MIXPANEL_CONFIG_OVERRIDES={"record_sessions_percent": 0.5,"record_heatmap_data": true}
NEXT_PUBLIC_NETWORK_CURRENCY_DECIMALS=18
NEXT_PUBLIC_NETWORK_CURRENCY_NAME=ETH
NEXT_PUBLIC_NETWORK_CURRENCY_SYMBOL=ETH
NEXT_PUBLIC_NETWORK_ICON=https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/network-icons/mega-eth-light.svg
NEXT_PUBLIC_NETWORK_ICON_DARK=https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/network-icons/mega-eth-dark.svg
NEXT_PUBLIC_NETWORK_ID=6342
NEXT_PUBLIC_NETWORK_LOGO=https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/network-logos/mega-eth-light.svg
NEXT_PUBLIC_NETWORK_LOGO_DARK=https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/network-logos/mega-eth-dark.svg
NEXT_PUBLIC_NETWORK_NAME=MEGA Testnet
NEXT_PUBLIC_NETWORK_RPC_URL=https://carrot.megaeth.com/rpc
NEXT_PUBLIC_NETWORK_SHORT_NAME=MEGA Testnet
NEXT_PUBLIC_OG_ENHANCED_DATA_ENABLED=true
NEXT_PUBLIC_OG_IMAGE_URL=https://raw.githubusercontent.com/blockscout/frontend-configs/main/configs/og-images/mega-eth.png
NEXT_PUBLIC_PUZZLE_GAME_BADGE_CLAIM_LINK=https://badges.blockscout.com/mint/capyPuzzleBadge
NEXT_PUBLIC_STATS_API_BASE_PATH=/stats-service
NEXT_PUBLIC_STATS_API_HOST=https://megaeth-testnet.blockscout.com
NEXT_PUBLIC_TRANSACTION_INTERPRETATION_PROVIDER=blockscout
NEXT_PUBLIC_VIEWS_CONTRACT_LANGUAGE_FILTERS=['solidity','vyper','yul','geas']
NEXT_PUBLIC_VIEWS_TOKEN_SCAM_TOGGLE_ENABLED=true
NEXT_PUBLIC_VISUALIZE_API_HOST=https://visualizer.services.blockscout.com